# .github/CODEOWNERS

# This file uses a pattern-based syntax to define code owners.
# Each line maps a file or directory pattern to one or more owners.
# Owners can be GitHub usernames or team names (prefixed with @).

# Lines starting with # are comments.

# 1. Default owner for all files
# This rule applies to all files in the repository if no other more specific
# rule matches. It's good practice to have a default.
* @mmesquita23 @BleckWolf25

# 2. Specific files
# Assign ownership for a specific file.
/README.md @BleckWolf25
