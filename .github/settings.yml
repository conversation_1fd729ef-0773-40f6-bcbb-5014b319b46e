# .github/settings.yml

# This file is used by the Probot Settings App to manage repository settings.
# It allows you to keep your repository configuration in version control.
# For more details: https://probot.github.io/apps/settings/

repository:
  # A short description of the repository that will show up on GitHub
  description: "CresceFeliz é uma plataforma onde os parentes de crianças podem encontrar escolas, creches e instituições de ensino que atendem às necessidades de seus filhos."

  # A URL with more information about the repository
  # homepage: "https://crescefeliz.com"

  # Collaborators: give specific users access to the repository.
  collaborators:
    - username: mmesquita23
      permission: push
    - username: BleckWolf25
      permission: push

  # Default branch for the repository
  default_branch: "main"

  # Whether issues are enabled
  has_issues: true

  # Whether projects are enabled
  has_projects: true

  # Whether the wiki is enabled
  has_wiki: false

  # Whether to allow squash merging pull requests
  allow_squash_merge: true

  # Whether to allow merge committing pull requests
  allow_merge_commit: true

  # Whether to allow rebase merging pull requests
  allow_rebase_merge: true

  # Whether to delete head branches when pull requests are merged
  delete_branch_on_merge: false

  # Topics for the repository (tags)
  topics: "nextjs, typescript, pnpm, react, web, linters, eslint, prettier, tailwindcss, testing-library, playwright, husky, commitlint, conventional-commits"
