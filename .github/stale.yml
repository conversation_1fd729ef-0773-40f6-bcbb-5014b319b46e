# .github/stale.yml

# Configuration for the 'stale' GitHub Action.
# This action marks issues and pull requests that have been inactive
# for a specified period and can optionally close them.
# For more details: https://github.com/actions/stale

# Number of days of inactivity before an issue/PR becomes stale
days-before-stale: 60

# Number of days of inactivity before a stale issue/PR is closed
days-before-close: 7

# Message to post when an issue/PR becomes stale
stale-issue-message: >
  This issue has been automatically marked as stale because it has not had
  recent activity. It will be closed if no further activity occurs.
  Thank you for your contributions.

stale-pr-message: >
  This pull request has been automatically marked as stale because it has not had
  recent activity. It will be closed if no further activity occurs.
  Thank you for your contributions.

# Label to apply when an issue/PR becomes stale
stale-issue-label: "stale"
stale-pr-label: "stale"

# Issues/PRs with these labels will never be marked as stale
exempt-issue-labels:
  - "bug"
  - "enhancement"
  - "pinned"
  - "security"
exempt-pr-labels:
  - "WIP" # Work In Progress
  - "pinned"

# Close issues/PRs that are marked as stale and have had no activity
close-issue-message: "This issue was closed because it has been stalled for 7 days with no activity."
close-pr-message: "This pull request was closed because it has been stalled for 7 days with no activity."

# Schedule for the action to run.
schedule:
  cron: '0 0 * * *' # Run daily at midnight UTC
