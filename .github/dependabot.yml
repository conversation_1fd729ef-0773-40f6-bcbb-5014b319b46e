# .github/dependabot.yml

# To get started with <PERSON>penda<PERSON>, you'll need to specify which package ecosystems
# to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  # Enable updates for pnpm dependencies
  - package-ecosystem: "npm" # Use "npm" for pnpm projects as it uses npm registry
    directory: "/" # Location of your package.json and pnpm-lock.yaml
    schedule:
      interval: "weekly" # Check for updates weekly
    target-branch: "dev" # The branch to create pull requests against
    labels:
      - "dependencies"
      - "pnpm"
    commit-message:
      prefix: "fix" # Prefix for commit messages (e.g., "fix(deps): update next.js")
      include: "scope" # Include the package scope in the commit message

  # Enable updates for GitHub Actions workflows
  - package-ecosystem: "github-actions"
    directory: "/" # Location of your .github/workflows directory
    schedule:
      interval: "weekly" # Check for updates weekly
    target-branch: "main"
    labels:
      - "dependencies"
      - "github-actions"
    commit-message:
      prefix: "chore" # Prefix for commit messages (e.g., "chore(deps): update action")
      include: "scope"
