{
  // === EDITOR CONFIGURATION ===
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.formatOnType": true,
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.trimAutoWhitespace": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.fixAll.stylelint": "explicit",
    "source.organizeImports": "explicit",
    "source.removeUnusedImports": "explicit",
    "source.sortMembers": "explicit"
  },
  "editor.quickSuggestions": {
    "strings": true,
    "comments": false,
    "other": true
  },
  "editor.suggest.insertMode": "replace",
  "editor.acceptSuggestionOnCommitCharacter": false,
  "editor.acceptSuggestionOnEnter": "on",
  "editor.wordWrap": "on",
  "editor.rulers": [
    90,
    130
  ],
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairs": "active",
  "editor.inlineSuggest.enabled": true,
  "editor.linkedEditing": true,
  "editor.minimap.enabled": true,
  "editor.minimap.showSlider": "always",
  "editor.semanticHighlighting.enabled": true,
  // === INTELLISENSE & AUTOCOMPLETE ===
  "typescript.suggest.autoImports": true,
  "typescript.suggest.includePackageJsonAutoImports": "auto",
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "typescript.inlayHints.functionLikeReturnTypes.enabled": true,
  "typescript.inlayHints.parameterNames.enabled": "all",
  "typescript.inlayHints.variableTypes.enabled": true,
  "javascript.suggest.autoImports": true,
  "javascript.updateImportsOnFileMove.enabled": "always",
  // === LINTING & VALIDATION ===
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "json",
    "jsonc"
  ],
  "eslint.format.enable": true,
  "eslint.lintTask.enable": true,
  "eslint.run": "onSave",
  "eslint.codeAction.showDocumentation": {
    "enable": true
  },
  "stylelint.validate": [
    "css",
    "scss",
    "postcss",
    "less"
  ],
  "stylelint.snippet": [
    "css",
    "scss",
    "postcss"
  ],
  // === TAILWIND CSS CONFIGURATION ===
  "tailwindCSS.includeLanguages": {
    "typescript": "javascript",
    "typescriptreact": "javascript",
    "plaintext": "javascript"
  },
  "tailwindCSS.experimental.classRegex": [
    [
      "clsx\\(([^)]*)\\)",
      "\"([^\"]*)\""
    ],
    [
      "classNames\\(([^)]*)\\)",
      "\"([^\"]*)\""
    ],
    [
      "cn\\(([^)]*)\\)",
      "\"([^\"]*)\""
    ],
    [
      "cva\\(([^)]*)\\)",
      "[\"'`]([^\"'`]*).*?[\"'`]"
    ],
    [
      "twMerge\\(([^)]*)\\)",
      "\"([^\"]*)\""
    ]
  ],
  "tailwindCSS.emmetCompletions": true,
  "tailwindCSS.hovers": true,
  "tailwindCSS.suggestions": true,
  "tailwindCSS.colorDecorators": true,
  "tailwindCSS.validate": true,
  // === FILE ASSOCIATIONS ===
  "files.associations": {
    "*.css": "tailwindcss",
    "*.mdx": "markdown",
    "*.env*": "dotenv",
    ".env*": "dotenv",
    "*.json": "jsonc",
    "*.tsx": "typescriptreact",
    "*.ts": "typescript"
  },
  // === WORKBENCH & UI ===
  "workbench.iconTheme": "vscode-icons",
  "workbench.editor.enablePreview": false,
  "workbench.editor.closeOnFileDelete": true,
  "workbench.editor.highlightModifiedTabs": true,
  "workbench.startupEditor": "newUntitledFile",
  "workbench.tree.indent": 20,
  "workbench.tree.renderIndentGuides": "always",
  // === SEARCH & EXPLORER ===
  "search.exclude": {
    "**/node_modules": true,
    "**/bower_components": true,
    "**/.next": true,
    "**/dist": true,
    "**/build": true,
    "**/.git": true,
    "**/.vscode": false,
    "**/coverage": true,
    "**/.nyc_output": true,
    "**/tmp": true,
    "**/*.log": true
  },
  "files.exclude": {
    "**/.git": true,
    "**/.DS_Store": true,
    "**/node_modules": true,
    "**/.next": true,
    "**/dist": true,
    "**/build": true,
    "**/coverage": true
  },
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/**": true,
    "**/.next/**": true,
    "**/dist/**": true,
    "**/build/**": true
  },
  // === BETTER COMMENTS CONFIGURATION ===
  "better-comments.multilineComments": true,
  "better-comments.highlightPlainText": false,
  "better-comments.tags": [
    {
      "tag": "!",
      "color": "#FF2D00",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "?",
      "color": "#3498DB",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "//",
      "color": "#474747",
      "strikethrough": true,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "todo",
      "color": "#FF8C00",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    },
    {
      "tag": "*",
      "color": "#98C379",
      "strikethrough": false,
      "underline": false,
      "backgroundColor": "transparent",
      "bold": false,
      "italic": false
    }
  ],
  // === NEXT.JS SPECIFIC ===
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.enablePromptUseWorkspaceTsdk": true,
  "typescript.preferences.quoteStyle": "single",
  "javascript.preferences.quoteStyle": "single",
  // === EMMET CONFIGURATION ===
  "emmet.includeLanguages": {
    "javascript": "javascriptreact",
    "typescript": "typescriptreact"
  },
  "emmet.triggerExpansionOnTab": true,
  "emmet.showAbbreviationSuggestions": true,
  "emmet.showExpandedAbbreviation": "always",
  "emmet.showSuggestionsAsSnippets": true,
  // === GIT CONFIGURATION ===
  "git.enableSmartCommit": true,
  "git.autofetch": true,
  "git.confirmSync": false,
  "git.enableStatusBarSync": true,
  "git.postCommitCommand": "none",
  "git.showPushSuccessNotification": true,
  "git.suggestSmartCommit": false,
  "git.ignoreLegacyWarning": true,
  // === TERMINAL CONFIGURATION ===
  "terminal.integrated.defaultProfile.windows": "PowerShell",
  "terminal.integrated.fontSize": 14,
  "terminal.integrated.fontFamily": "Fira Code, Consolas, 'Courier New', monospace",
  "terminal.integrated.cwd": "${workspaceFolder}",
  "terminal.integrated.env.windows": {},
  // === PERFORMANCE & OPTIMIZATION ===
  "npm.packageManager": "auto",
  "debug.allowBreakpointsEverywhere": true,
  "debug.openDebug": "openOnDebugBreak",
  "debug.showInStatusBar": "always",
  // === BREADCRUMBS ===
  "breadcrumbs.enabled": true,
  "breadcrumbs.showFiles": true,
  "breadcrumbs.showSymbols": true,
  "breadcrumbs.symbolPath": "on",
  "breadcrumbs.symbolSortOrder": "position",
  // === PROBLEMS & DIAGNOSTICS ===
  "problems.decorations.enabled": true,
  "problems.showCurrentInStatus": true,
  "problems.sortOrder": "severity",
  // === OUTLINE & SYMBOLS ===
  "outline.showFiles": true,
  "outline.showModules": true,
  "outline.showClasses": true,
  "outline.showFunctions": true,
  "outline.showVariables": true,
  // === LANGUAGE SPECIFIC OVERRIDES ===
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.suggest.insertMode": "replace"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.suggest.insertMode": "replace"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.suggest.insertMode": "replace"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.suggest.insertMode": "replace"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.quickSuggestions": {
      "strings": true
    }
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.quickSuggestions": {
      "strings": true
    }
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.suggest.insertMode": "replace"
  },
  "[scss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.suggest.insertMode": "replace"
  },
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.suggest.insertMode": "replace"
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.wordWrap": "on",
    "editor.quickSuggestions": {
      "other": "on",
      "comments": "on",
      "strings": "on"
    }
  },
  "[tailwindcss]": {
    "editor.quickSuggestions": {
      "strings": true
    }
  }
}
