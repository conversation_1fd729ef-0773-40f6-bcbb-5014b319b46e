{
  // Recommended extensions for this workspace
  "recommendations": [
    "esbenp.prettier-vscode",         // Prettier - Code formatter
    "dbaeumer.vscode-eslint",         // ESLint
    "stylelint.vscode-stylelint",     // Stylelint
    "bradlc.vscode-tailwindcss",      // Tailwind CSS IntelliSense
    "axe-linter.vscode-axe-linter",   // Axe Accessibility Linter
    "vscode-icons-team.vscode-icons", // VSCode Icons
    "ms-azuretools.vscode-docker"     // Docker
  ]
}
