# Cresce Feliz

> **Modern Educational Platform** - Connecting schools, photographers, and families through superior accessibility, image management, privacy and user experience.

## 🚀 Vision

Cresce Feliz revolutionizes educational platforms by providing schools with professional-grade image management tools, user experience enhancements, and families with accessible, and secure school finding.

## 🎯 Core Objectives

- **Superior Image Quality**: Professional-grade photo processing with automatic optimization
- **Enhanced User Experience**: Intuitive interfaces designed for educational environments
- **Advanced Filtering**: Sophisticated search and discovery mechanisms
- **School Management**: Comprehensive administrative tools for educational institutions
- **Privacy-First**: GDPR-compliant architecture with automatic face blurring protection
- **School Finding**: Easy-to-use platform for families to discover and connect with schools for their children

## 🏗️ Architecture Overview

### Technology Stack

- **Frontend**: Next.js 15+ with <PERSON>pp Router, React 19+, TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components
- **Backend**: Firebase (Auth, Firestore, Storage, Functions)
- **Infrastructure**: Google Cloud Platform with Edge Computing
- **Development**: Turborepo monorepo, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, EditorConfig
- **Version Control**: Git with GitHub for collaboration
- **Testing**: Playwright for end-to-end testing.

### Key Features

#### 🏫 **School Management**

- Administrative dashboards for directors and staff
- Event management and photographer coordination
- Photo approval workflows with 72-hour SLA
- Revenue tracking and performance analytics

#### 📸 **Photography Workflow**

- Photographer authorization and verification system
- Bulk photo upload with automatic processing
- Real-time upload notifications and status tracking
- Quality control and approval mechanisms

#### 🔍 **Discovery Platform**

- Advanced school search and filtering
- Geolocation-based recommendations
- Review and rating systems
- Event-based photo galleries

#### 🛡️ **Privacy & Security**

- GDPR-compliant data processing
- Automatic face blurring for student privacy
- Role-based access control (RBAC)
- Secure image storage with CDN distribution

## 📋 Project Structure

```zsh
CresceFeliz/
├── apps/web/           # Next.js main application
├── apps/admin/         # Administrative dashboard
├── packages/shared/    # Shared utilities and types
├── packages/ui/        # Reusable UI components
├── functions/          # Firebase Cloud Functions
├── docs/              # Documentation
└── tests/             # Testing suites
```

For detailed architecture information, see [Project Structure](./docs/PROJECT_STRUCTURE.md).

## 🚀 Quick Start

### Prerequisites

- Node.js 22.x or higher
- pnpm 10.x or higher
- Firebase CLI
- Google Cloud SDK

### Installation

```bash
# Clone the repository
git clone https://github.com/your-org/cresce-feliz.git
cd cresce-feliz

# Install dependencies
pnpm install

# Set up environment variables
cp apps/web/.env.example apps/web/.env.local

# Initialize Firebase
firebase login
firebase init

# Start development server
pnpm dev
```

### Development Commands

```bash
# Development
pnpm dev              # Start all applications
pnpm dev:web          # Start web application only
pnpm dev:admin        # Start admin dashboard only

# Building
pnpm build            # Build all applications
pnpm build:web        # Build web application
pnpm build:admin      # Build admin dashboard

# Testing
pnpm test             # Run all tests
pnpm test:unit        # Run unit tests
pnpm test:e2e         # Run end-to-end tests

# Linting & Formatting
pnpm lint             # Run ESLint
pnpm format           # Run Prettier
pnpm type-check       # TypeScript type checking
```

## 📚 Documentation

- [**Design Document**](./DESIGN.md) - Comprehensive project design and architecture
- [**API Documentation**](./docs/API.md) - Backend API specifications
- [**Deployment Guide**](./docs/DEPLOYMENT.md) - Production deployment instructions
- [**Security Guidelines**](./docs/SECURITY.md) - Security best practices and protocols
- [**Contributing Guide**](./docs/CONTRIBUTING.md) - Development workflow and contribution guidelines

## 🌍 Internationalization

Currently supported languages:

- **Portuguese** (PT) - Primary
- **English** (EN) - Secondary
- **Spanish** (ES) - Tertiary
- **French** (FR) - Quaternary

Future expansion planned for additional European and global markets.

## 🔒 Security & Privacy

- **GDPR Compliance**: Automatic data protection and privacy controls
- **Student Privacy**: Mandatory face blurring for underage individuals
- **Data Encryption**: End-to-end encryption for sensitive information
- **Access Control**: Multi-level authentication and authorization
- **Audit Logging**: Comprehensive activity tracking and compliance reporting

## 📊 Performance Targets

- **Core Web Vitals**: LCP < 2.5s, FID < 100ms, CLS < 0.1
- **Image Optimization**: Automatic format conversion and compression
- **Edge Computing**: Global CDN distribution for optimal performance
- **Scalability**: Support for 1,000-10,000 concurrent users

## 🤝 Contributing

We welcome contributions from the community. Please read our [Contributing Guide](./docs/CONTRIBUTING.md) for development workflow, coding standards, and pull request procedures.

### Development Workflow

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the Commercial License - see the [LICENSE](./LICENSE) file for details.

## 🆘 Support

- **Documentation**: [Project Wiki](https://github.com/your-org/CresceFeliz/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-org/CresceFeliz/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/CresceFeliz/discussions)
- **Email**: <<EMAIL>>

## 📈 Roadmap

### Phase 1: Core Platform (Q1 2025)

- ✅ Project architecture and setup
- 🔄 Authentication and authorization system
- 🔄 School registration and management
- 🔄 Basic photo upload and processing

### Phase 2: Enhanced Features (Q2 2025)

- 📋 Advanced search and filtering
- 📋 Real-time notifications
- 📋 Payment integration
- 📋 Mobile-responsive design

### Phase 3: Advanced Management (Q3 2025)

- 📋 Comprehensive school dashboards
- 📋 Analytics and reporting
- 📋 Multi-language support expansion
- 📋 API for third-party integrations

### Phase 4: Scale & Optimize (Q4 2025-2026)

- 📋 Performance optimization
- 📋 Advanced image processing
- 📋 Machine learning integration
- 📋 International market expansion

### Built with ❤️ by the CresceFeliz Team
