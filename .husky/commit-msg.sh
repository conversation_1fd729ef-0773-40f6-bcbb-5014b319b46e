#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Check if this is a JavaScript/TypeScript project
if [ -f "package.json" ]; then
  echo "🔍 Validating commit message format..."
  npx --no -- commitlint --edit $1
else
  echo "⚠️ No package.json found. Using basic commit message validation."

  # Basic commit message validation for non-JS projects
  commit_msg=$(cat $1)
  if [[ ! "$commit_msg" =~ ^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: ]]; then
    echo "⛔ Error: Commit message must follow conventional commits format."
    echo "Example: feat(login): add password validation"
    exit 1
  fi
fi