#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Check if this is a JavaScript/TypeScript project
if [ -f "package.json" ]; then
  echo "🔍 Running pre-commit hooks for JavaScript/TypeScript project..."
  npx lint-staged
else
  echo "⚠️ No package.json found. Skipping JavaScript/TypeScript specific checks."
  # Add fallback checks for other project types if needed
fi

# Run general pre-commit checks that apply to all projects
echo "🔍 Running general pre-commit checks..."

# Check for large files
find . -type f -not -path "*/node_modules/*" -not -path "*/\.git/*" -size +5M | while read file; do
  echo "⛔ Error: File $file is too large (>5MB). Please add to .gitignore or reduce its size."
  exit 1
done

# Check for merge conflict markers
if grep -r "<<<<<<< HEAD" --include="*.{js,jsx,ts,tsx,json,css,scss,md,html,py,rb,go}" .; then
  echo "⛔ Error: Merge conflict markers detected. Please resolve conflicts before committing."
  exit 1
fi

# Check for sensitive information
if grep -r "password\|secret\|api[_-]key\|token" --include="*.{js,jsx,ts,tsx,json,env}" .; then
  echo "⚠️ Warning: Possible sensitive information detected. Please verify before committing."
  # Uncomment to block commits with sensitive info
  # exit 1
fi

echo "✅ Pre-commit checks passed!"