#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Check if this is a JavaScript/TypeScript project
if [ -f "package.json" ]; then
  echo "🔍 Running tests before push..."
  npm test

  echo "🔍 Running linting checks..."
  npm run lint
else
  echo "⚠️ No package.json found. Skipping JavaScript/TypeScript specific checks."
  # Add fallback checks for other project types if needed
fi

# Check branch naming convention
branch_name=$(git rev-parse --abbrev-ref HEAD)
if [[ ! "$branch_name" =~ ^(feature|bugfix|hotfix)/ ]]; then
    echo "⛔ Error: Branch name '$branch_name' must start with feature/, bugfix/, or hotfix/."
    exit 1
fi

echo "✅ Pre-push checks passed!"