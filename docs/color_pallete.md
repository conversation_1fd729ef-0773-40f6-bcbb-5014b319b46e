# Crescer Feliz – Website Color Palette

A clean, modern palette based on the <PERSON><PERSON>cer Feliz logo, emphasizing minty greens and warm yellows
for a friendly, fresh look.

---

## 🎨 Primary Colors

- **Mint Green** – `#7ACBC1`  
  Fresh, calm, modern. Great for headers, buttons, or backgrounds.

- **Warm Yellow** – `#F6C75A`  
  Friendly and optimistic. Use for accents like CTAs, highlights, and icons.

---

## 🌿 Secondary / Supporting Colors

- **Soft Sage** – `#A8DEDA`  
  A lighter mint variant for hover states, background panels, or overlays.

- **Pale Cream** – `#F5EFE9`  
  Clean neutral for page backgrounds or large sections—keeps it easy on the eyes.

---

## 🔧 Accent / Utility Colors

- **Warm Gray** – `#A8A096`  
  Use for body text, dividers, or footers. Keeps contrast high without harshness.

- **Coral Pink** – `#EF8A7E`  
  Optional pop color when you need a draw-away or error state that's still friendly.

---

## 📋 Color Palette Table

| Role                     | Color       | Hex       |
| ------------------------ | ----------- | --------- |
| Primary Mint             | Mint Green  | `#7ACBC1` |
| Primary Accent           | Warm Yellow | `#F6C75A` |
| Light Variant            | Soft Sage   | `#A8DEDA` |
| Background Neutral       | Pale Cream  | `#F5EFE9` |
| Text / Secondary Usage   | Warm Gray   | `#A8A096` |
| Optional Attention Color | Coral Pink  | `#EF8A7E` |

---

## 📌 Usage Strategy

- **Headers / Nav Bars**: Mint Green (`#7ACBC1`) for vibrancy and brand alignment.
- **CTAs / Icons / Highlights**: Warm Yellow (`#F6C75A`) to attract eyes without shouting.
- **Hover / Subtle Overlays**: Soft Sage (`#A8DEDA`) for nuanced interactive feedback.
- **Backgrounds**: Pale Cream (`#F5EFE9`) keeps pages looking clean and elevated.
- **Text**: Warm Gray (`#A8A096`) ensures readability and ties into the earth-friendly vibe.
- **Alerts / Promo Pushes**: Coral Pink (`#EF8A7E`) works as a contrasting but cohesive accent.

---

**Pro Tip:** Stick to **3–4 colors** per view to avoid visual clutter.  
Always check accessibility contrast with tools like [axe](https://www.deque.com/axe/) or
[Contrast Checker](https://contrastchecker.com/).
