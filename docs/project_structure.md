# Cresce-Feliz Project Structure

## Architecture Overview

```zsh
cresce-feliz/
├── .github/
│   └── workflows/
│       ├── ci.yml
│       ├── cd-staging.yml
│       └── cd-production.yml
├── apps/
│   ├── src/                          # Next.js main application
│   │   ├── .next/
│   │   ├── public/
│   │   │   ├── images/
│   │   │   ├── icons/
│   │   │   └── manifest.json
│   │   ├── app/                  # App Router (Next.js 13+)
│   │   │   │   ├── (auth)/
│   │   │   │   │   ├── login/
│   │   │   │   │   └── register/
│   │   │   │   ├── (dashboard)/
│   │   │   │   │   ├── school/
│   │   │   │   │   │   ├── [schoolId]/
│   │   │   │   │   │   │   ├── events/
│   │   │   │   │   │   │   ├── photos/
│   │   │   │   │   │   │   ├── settings/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   └── photographer/
│   │   │   │   │       ├── [photographerId]/
│   │   │   │   │       └── page.tsx
│   │   │   │   ├── (public)/
│   │   │   │   │   ├── schools/
│   │   │   │   │   │   ├── [schoolId]/
│   │   │   │   │   │   │   ├── events/
│   │   │   │   │   │   │   │   └── [eventId]/
│   │   │   │   │   │   │   └── page.tsx
│   │   │   │   │   │   └── page.tsx
│   │   │   │   │   ├── search/
│   │   │   │   │   └── page.tsx
│   │   │   │   ├── api/
│   │   │   │   │   ├── auth/
│   │   │   │   │   ├── schools/
│   │   │   │   │   ├── photographers/
│   │   │   │   │   ├── photos/
│   │   │   │   │   ├── events/
│   │   │   │   │   ├── webhooks/
│   │   │   │   │   └── health/
│   │   │   │   ├── globals.css
│   │   │   │   ├── layout.tsx
│   │   │   │   ├── loading.tsx
│   │   │   │   ├── error.tsx
│   │   │   │   └── not-found.tsx
│   │   │   ├── components/
│   │   │   │   ├── ui/               # Shadcn/ui components
│   │   │   │   │   ├── button.tsx
│   │   │   │   │   ├── input.tsx
│   │   │   │   │   ├── dialog.tsx
│   │   │   │   │   ├── toast.tsx
│   │   │   │   │   └── index.ts
│   │   │   │   ├── forms/
│   │   │   │   │   ├── school-registration.tsx
│   │   │   │   │   ├── photographer-registration.tsx
│   │   │   │   │   ├── photo-upload.tsx
│   │   │   │   │   └── review-form.tsx
│   │   │   │   ├── layout/
│   │   │   │   │   ├── header.tsx
│   │   │   │   │   ├── footer.tsx
│   │   │   │   │   ├── sidebar.tsx
│   │   │   │   │   └── navigation.tsx
│   │   │   │   ├── features/
│   │   │   │   │   ├── school-profile/
│   │   │   │   │   │   ├── school-card.tsx
│   │   │   │   │   │   ├── school-gallery.tsx
│   │   │   │   │   │   └── school-reviews.tsx
│   │   │   │   │   ├── photo-management/
│   │   │   │   │   │   ├── photo-grid.tsx
│   │   │   │   │   │   ├── photo-upload.tsx
│   │   │   │   │   │   ├── photo-approval.tsx
│   │   │   │   │   │   └── photo-filters.tsx
│   │   │   │   │   └── search/
│   │   │   │   │       ├── search-bar.tsx
│   │   │   │   │       ├── search-filters.tsx
│   │   │   │   │       └── search-results.tsx
│   │   │   │   └── common/
│   │   │   │       ├── loading-spinner.tsx
│   │   │   │       ├── error-boundary.tsx
│   │   │   │       ├── image-with-fallback.tsx
│   │   │   │       └── seo-head.tsx
│   │   │   ├── hooks/
│   │   │   │   ├── use-auth.ts
│   │   │   │   ├── use-schools.ts
│   │   │   │   ├── use-photos.ts
│   │   │   │   ├── use-realtime.ts
│   │   │   │   └── use-debounce.ts
│   │   │   ├── lib/
│   │   │   │   ├── firebase/
│   │   │   │   │   ├── config.ts
│   │   │   │   │   ├── auth.ts
│   │   │   │   │   ├── firestore.ts
│   │   │   │   │   ├── storage.ts
│   │   │   │   │   └── functions.ts
│   │   │   │   ├── auth/
│   │   │   │   │   ├── session.ts
│   │   │   │   │   ├── rbac.ts
│   │   │   │   │   └── permissions.ts
│   │   │   │   ├── image-processing/
│   │   │   │   │   ├── optimization.ts
│   │   │   │   │   ├── face-blur.ts
│   │   │   │   │   └── variants.ts
│   │   │   │   ├── utils/
│   │   │   │   │   ├── constants.ts
│   │   │   │   │   ├── formatters.ts
│   │   │   │   │   ├── validators.ts
│   │   │   │   │   └── cn.ts
│   │   │   │   ├── types/
│   │   │   │   │   ├── auth.ts
│   │   │   │   │   ├── school.ts
│   │   │   │   │   ├── photographer.ts
│   │   │   │   │   ├── photo.ts
│   │   │   │   │   └── api.ts
│   │   │   │   └── validations/
│   │   │   │       ├── auth.ts
│   │   │   │       ├── school.ts
│   │   │   │       ├── photographer.ts
│   │   │   │       └── photo.ts
│   │   │   ├── store/
│   │   │   │   ├── slices/
│   │   │   │   │   ├── auth-slice.ts
│   │   │   │   │   ├── school-slice.ts
│   │   │   │   │   ├── photo-slice.ts
│   │   │   │   │   └── ui-slice.ts
│   │   │   │   ├── providers/
│   │   │   │   │   ├── auth-provider.tsx
│   │   │   │   │   ├── theme-provider.tsx
│   │   │   │   │   └── toast-provider.tsx
│   │   │   │   └── index.ts
│   │   │   └── styles/
│   │   │       ├── globals.css
│   │   │       └── components.css
│   │   ├── .env.local
│   │   ├── .env.example
│   │   ├── next.config.js
│   │   ├── package.json
│   │   ├── tailwind.config.ts
│   │   ├── tsconfig.json
│   │   └── components.json
├── functions/                        # Firebase Cloud Functions
│   ├── src/
│   │   ├── triggers/
│   │   │   ├── photo-upload.ts
│   │   │   ├── photo-approval.ts
│   │   │   └── user-registration.ts
│   │   ├── scheduled/
│   │   │   ├── cleanup-expired.ts
│   │   │   └── backup-data.ts
│   │   ├── callable/
│   │   │   ├── process-image.ts
│   │   │   ├── send-notification.ts
│   │   │   └── generate-report.ts
│   │   ├── utils/
│   │   │   ├── image-processing.ts
│   │   │   ├── email-templates.ts
│   │   │   └── notifications.ts
│   │   └── index.ts
│   ├── package.json
│   ├── tsconfig.json
│   └── .env
├── docs/
│   ├── API.md
│   ├── DEPLOYMENT.md
│   ├── DESIGN.md
│   └── PROJECT_STRUCTURE.md
├── scripts/
│   ├── setup.sh
│   ├── deploy.sh
│   ├── migrate.sh
│   └── backup.sh
├── tests/
│   ├── __mocks__/
│   ├── e2e/
│   │   ├── auth.spec.ts
│   │   ├── school-registration.spec.ts
│   │   └── photo-upload.spec.ts
│   ├── integration/
│   │   ├── api/
│   │   └── components/
│   └── unit/
│       ├── components/
│       ├── hooks/
│       └── utils/
├── .editorconfig
├── .eslintrc.js
├── .gitignore
├── .prettierrc
├── CONTRIBUTING.md
├── docker-compose.yml
├── Dockerfile
├── firebase.json
├── firestore.rules
├── firestore.indexes.json
├── storage.rules
├── package.json
├── pnpm-workspace.yaml
├── turbo.json
├── tsconfig.json
├── README.md
└── SECURITY.md
```

## Key Architectural Decisions

### 1. **Monorepo Structure with Turborepo**

- **Rationale**: Enables code sharing between web app, admin dashboard, and cloud functions
- **Benefits**: Consistent tooling, shared types, efficient builds
- **Trade-offs**: Increased complexity for smaller teams

### 2. **Next.js App Router Architecture**

- **Route Groups**: Logical separation of public, authenticated, and dashboard routes
- **Server Components**: Optimal performance for SSR requirements
- **API Routes**: Co-located with frontend for rapid development

### 3. **Firebase Integration Strategy**

- **Authentication**: Centralized user management with RBAC
- **Firestore**: Document-based data modeling for schools, photographers, events
- **Storage**: Image assets with automatic optimization pipeline
- **Functions**: Serverless backend for image processing and notifications

### 4. **State Management Architecture**

- **Zustand**: Lightweight state management for client-side state
- **React Query**: Server state management with caching and synchronization
- **Context Providers**: Authentication and theme management

### 5. **Image Processing Pipeline**

- **Client-side**: Initial validation and preview generation
- **Server-side**: Optimization, face blurring, variant generation
- **CDN**: Edge caching with Google Cloud CDN

### 6. **Internationalization Strategy**

- **Next.js i18n**: Built-in routing and locale detection
- **React-i18next**: Translation management and context switching
- **Locale-specific**: URL structures and content delivery

### 7. **Security Architecture**

- **Authentication**: Firebase Auth with custom claims for RBAC
- **Authorization**: Middleware-based route protection
- **Data Privacy**: GDPR compliance with automatic face blurring
- **API Security**: Rate limiting and input validation

### 8. **Performance Optimization**

- **Image Optimization**: Next.js Image component with Firebase Storage
- **Code Splitting**: Route-based and component-based splitting
- **Caching**: Multi-layer caching strategy (CDN, server, client)
- **Edge Computing**: Google Cloud Functions for global performance

### 9. **Development Workflow**

- **TypeScript**: End-to-end type safety
- **ESLint + Prettier**: Code quality and formatting
- **Testing**: Unit, integration, and E2E testing strategies
- **CI/CD**: Automated testing, building, and deployment

### 10. **Scalability Considerations**

- **Database**: Firestore with proper indexing and query optimization
- **Storage**: Firebase Storage with automatic scaling
- **Functions**: Serverless architecture for automatic scaling
- **Monitoring**: Firebase Analytics and Google Cloud Monitoring
