{"name": "crescefe<PERSON>z", "version": "1.0.0", "description": "> **Modern Educational Platform** - Connecting schools, photographers, and families through superior accessibility, image management, privacy and user experience.", "main": "src/index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint . --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix"}, "engines": {"node": ">=22 <23"}, "keywords": [], "author": "mmesquita23 <<EMAIL>>", "license": "Commercial", "type": "module", "dependencies": {"class-variance-authority": "^0.7.1", "@react-aria/ssr": "^3.9.9", "@react-aria/visually-hidden": "^3.8.25", "babel-plugin-react-compiler": "19.1.0-rc.2", "bootstrap-icons": "^1.13.1", "clsx": "^2.1.1", "firebase": "^11.10.0", "framer-motion": "^12.23.3", "intl-messageformat": "^10.7.16", "next": "^15.3.5", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0"}, "devDependencies": {"@eslint/compat": "^1.3.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.31.0", "@next/eslint-plugin-next": "^15.3.5", "@react-types/shared": "^3.30.0", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.0.13", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-config-next": "^15.3.5", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "postcss": "^8.5.6", "postcss-scss": "^4.0.9", "prettier": "^3.6.2", "stylelint": "^16.21.1", "stylelint-config-standard": "^38.0.0", "stylelint-config-tailwindcss": "^1.0.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}, "repository": {"type": "git", "url": "git+https://github.com/BleckWolf25/CresceFeliz.git"}, "bugs": {"url": "https://github.com/BleckWolf25/CresceFeliz/issues"}, "homepage": "https://github.com/BleckWolf25/CresceFeliz#readme", "packageManager": "pnpm@10.13.1+sha512.37ebf1a5c7a30d5fabe0c5df44ee8da4c965ca0c5af3dbab28c3a1681b70a256218d05c81c9c0dcf767ef6b8551eb5b960042b9ed4300c59242336377e01cfad"}