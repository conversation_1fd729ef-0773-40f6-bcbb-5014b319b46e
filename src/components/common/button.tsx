import React from 'react';
import { tv, type VariantProps } from 'tailwind-variants';

// Button style variants – mirrors admin version to keep consistency across app
export const buttonVariants = tv({
  base: 'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',
  variants: {
    variant: {
      default:
        'bg-gradient-to-r from-green-600 to-yellow-500 text-white shadow hover:from-green-700 hover:to-yellow-600',
      destructive: 'bg-red-600 text-white shadow-sm hover:bg-red-700',
      outline:
        'border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground',
      secondary:
        'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80',
      ghost: 'hover:bg-accent hover:text-accent-foreground',
      link: 'text-primary underline-offset-4 hover:underline',
      success: 'bg-green-600 text-white shadow hover:bg-green-700',
      warning: 'bg-yellow-600 text-white shadow hover:bg-yellow-700',
    },
    size: {
      default: 'h-9 px-4 py-2',
      sm: 'h-8 rounded-md px-3 text-xs',
      lg: 'h-10 rounded-md px-8',
      icon: 'h-9 w-9',
    },
    loading: {
      true: 'relative pointer-events-none opacity-80',
      false: '',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'default',
    loading: false,
  },
});

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  isLoading?: boolean;
  loadingText?: string;
  variant?: VariantProps<typeof buttonVariants>['variant'];
  size?: VariantProps<typeof buttonVariants>['size'];
  loading?: VariantProps<typeof buttonVariants>['loading'];
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,

      isLoading = false,
      loadingText,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    return (
      <button
        ref={ref}
        className={buttonVariants({
          variant,
          size,
          loading: isLoading ? true : false,
          className,
        })}
        disabled={disabled || isLoading}
        {...props}
      >
        {isLoading && (
          <span className='absolute left-2 inline-flex h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent' />
        )}
        <span className={isLoading ? 'opacity-0' : ''}>{children}</span>
        {isLoading && loadingText && (
          <span className='ml-2 text-xs font-normal tracking-wide'>
            {loadingText}
          </span>
        )}
      </button>
    );
  }
);
Button.displayName = 'Button';

export default Button;
