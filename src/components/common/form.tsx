import { clsx } from 'clsx';
import React from 'react';

export interface FormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  direction?: 'vertical' | 'horizontal';
  gap?: 'sm' | 'md' | 'lg';
}

const gapMap = {
  sm: 'gap-3',
  md: 'gap-4',
  lg: 'gap-6',
};

export const Form: React.FC<FormProps> = ({
  className,
  children,
  direction = 'vertical',
  gap = 'md',
  onSubmit,
  ...rest
}) => {
  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit(e);
      }}
      className={clsx(
        'w-full',
        direction === 'vertical'
          ? 'flex flex-col'
          : 'grid grid-cols-1 md:grid-cols-2',
        gapMap[gap],
        className
      )}
      {...rest}
    >
      {children}
    </form>
  );
};

export interface FormFieldProps extends React.HTMLAttributes<HTMLDivElement> {
  fullWidth?: boolean;
  columns?: number; // for horizontal grid layout
}

export const FormField: React.FC<FormFieldProps> = ({
  className,
  children,
  fullWidth,
  columns,
  ...rest
}) => {
  return (
    <div
      className={clsx(
        'flex flex-col space-y-1',
        fullWidth && 'col-span-full',
        columns && `md:col-span-${columns}`,
        className
      )}
      {...rest}
    >
      {children}
    </div>
  );
};

export interface FormLabelProps
  extends React.LabelHTMLAttributes<HTMLLabelElement> {}
export const FormLabel: React.FC<FormLabelProps> = ({ className, ...rest }) => (
  <label
    className={clsx(
      'text-sm font-medium text-gray-700 dark:text-gray-300',
      className
    )}
    {...rest}
  />
);

export interface FormErrorProps
  extends React.HTMLAttributes<HTMLParagraphElement> {}
export const FormError: React.FC<FormErrorProps> = ({ className, ...rest }) => (
  <p
    className={clsx('text-xs font-medium text-red-600', className)}
    {...rest}
  />
);

export interface FormHelpProps
  extends React.HTMLAttributes<HTMLParagraphElement> {}
export const FormHelp: React.FC<FormHelpProps> = ({ className, ...rest }) => (
  <p className={clsx('text-xs text-gray-500', className)} {...rest} />
);

export interface FormActionsProps
  extends React.HTMLAttributes<HTMLDivElement> {}
export const FormActions: React.FC<FormActionsProps> = ({
  className,
  ...rest
}) => (
  <div
    className={clsx(
      'mt-4 flex flex-col-reverse gap-2 sm:flex-row sm:justify-end',
      className
    )}
    {...rest}
  />
);

export default Form;
