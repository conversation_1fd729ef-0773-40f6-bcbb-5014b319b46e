import React from 'react';
import { tv, type VariantProps } from 'tailwind-variants';

export const inputVariants = tv({
  base: 'flex w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50',
  variants: {
    variant: {
      default: 'border-gray-300 focus-visible:ring-green-500',
      error: 'border-red-500 focus-visible:ring-red-500',
      success: 'border-green-500 focus-visible:ring-green-500',
      warning: 'border-yellow-500 focus-visible:ring-yellow-500',
    },
    fieldSize: {
      sm: 'h-8 text-xs',
      default: 'h-9',
      lg: 'h-11 text-base',
    },
    state: {
      loading: 'animate-pulse cursor-progress',
      normal: '',
    },
  },
  defaultVariants: {
    variant: 'default',
    fieldSize: 'default',
    state: 'normal',
  },
});

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>,
    VariantProps<typeof inputVariants> {
  label?: string;
  error?: string;
  helperText?: string;
  isLoading?: boolean;
  variant?: VariantProps<typeof inputVariants>['variant'];
  fieldSize?: VariantProps<typeof inputVariants>['fieldSize'];
  state?: VariantProps<typeof inputVariants>['state'];
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      variant,
      fieldSize,
      label,
      error,
      helperText,
      isLoading,
      ...props
    },
    ref
  ) => {
    const inputId = React.useId();
    return (
      <div className='space-y-1'>
        {label && (
          <label
            htmlFor={inputId}
            className='block text-sm font-medium text-gray-700'
          >
            {label}
          </label>
        )}
        <div className='relative'>
          <input
            id={inputId}
            className={inputVariants({
              variant: error ? 'error' : variant,
              fieldSize,
              state: isLoading ? 'loading' : 'normal',
              className,
            })}
            ref={ref}
            aria-invalid={!!error}
            aria-describedby={error ? `${inputId}-error` : undefined}
            {...props}
          />
          {isLoading && (
            <span className='absolute w-4 h-4 text-gray-400 -translate-y-1/2 border-2 border-current rounded-full right-2 top-1/2 animate-spin border-t-transparent' />
          )}
        </div>
        {error && (
          <p id={`${inputId}-error`} className='text-xs text-red-600'>
            {error}
          </p>
        )}
        {helperText && !error && (
          <p className='text-xs text-gray-500'>{helperText}</p>
        )}
      </div>
    );
  }
);
Input.displayName = 'Input';

export default Input;
