import { clsx } from 'clsx';
import React from 'react';

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  padding?: 'none' | 'sm' | 'md' | 'lg';
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  interactive?: boolean;
  bordered?: boolean;
}

const paddingMap = {
  none: 'p-0',
  sm: 'p-3',
  md: 'p-4',
  lg: 'p-6',
};

const shadowMap = {
  none: '',
  sm: 'shadow-sm',
  md: 'shadow',
  lg: 'shadow-lg',
};

export const Card = React.forwardRef<HTMLDivElement, CardProps>(
  (
    {
      className,
      padding = 'md',
      shadow = 'sm',
      interactive = false,
      bordered = true,
      ...props
    },
    ref
  ) => {
    return (
      <div
        ref={ref}
        className={clsx(
          'relative rounded-lg bg-white/70 backdrop-blur-sm dark:bg-gray-900/60 transition-colors',
          bordered && 'border border-gray-200 dark:border-gray-700',
          paddingMap[padding],
          shadowMap[shadow],
          interactive &&
            'hover:shadow-md focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-green-500 cursor-pointer',
          className
        )}
        {...props}
      />
    );
  }
);
Card.displayName = 'Card';

export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {}
export const CardHeader: React.FC<CardHeaderProps> = ({
  className,
  ...rest
}) => (
  <div className={clsx('mb-3 flex flex-col space-y-1', className)} {...rest} />
);

export interface CardTitleProps
  extends React.HTMLAttributes<HTMLHeadingElement> {}
export const CardTitle: React.FC<CardTitleProps> = ({ className, ...rest }) => (
  <h3
    className={clsx(
      'text-base font-semibold tracking-tight text-gray-900 dark:text-gray-100',
      className
    )}
    {...rest}
  />
);

export interface CardDescriptionProps
  extends React.HTMLAttributes<HTMLParagraphElement> {}
export const CardDescription: React.FC<CardDescriptionProps> = ({
  className,
  ...rest
}) => (
  <p
    className={clsx('text-sm text-gray-500 dark:text-gray-400', className)}
    {...rest}
  />
);

export interface CardContentProps
  extends React.HTMLAttributes<HTMLDivElement> {}
export const CardContent: React.FC<CardContentProps> = ({
  className,
  ...rest
}) => <div className={clsx('space-y-3', className)} {...rest} />;

export interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {}
export const CardFooter: React.FC<CardFooterProps> = ({
  className,
  ...rest
}) => (
  <div
    className={clsx('mt-4 flex items-center justify-end space-x-2', className)}
    {...rest}
  />
);

export default Card;
