'use client';

import React, { useState } from 'react';
import {
  BsBook,
  BsBuilding,
  BsEye,
  BsEyeSlash,
  BsPeople,
  BsShield,
} from 'react-icons/bs';

import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Form,
  FormActions,
  FormField,
  FormHelp,
  FormLabel,
  Input,
} from '@/components/common';

export default function AdminLogin() {
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // Redirect to dashboard after successful login
    window.location.href = '/admin/dashboard';
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <div className='relative flex min-h-screen items-center justify-center bg-gradient-to-br from-green-50 via-emerald-50 to-yellow-50 px-4 py-10'>
      {/* Decorative blobs */}
      <div className='pointer-events-none absolute inset-0 overflow-hidden'>
        <div className='absolute -top-40 -right-40 h-80 w-80 animate-pulse rounded-full bg-gradient-to-br from-green-400/20 to-yellow-400/20 blur-3xl' />
        <div className='absolute -bottom-40 -left-40 h-80 w-80 animate-pulse rounded-full bg-gradient-to-br from-emerald-400/20 to-amber-400/20 blur-3xl delay-1000' />
      </div>

      <div className='relative w-full max-w-md'>
        <div className='mb-8 text-center'>
          <div className='mb-4 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-green-600 to-yellow-500 shadow-lg ring-1 ring-black/5'>
            <BsBuilding className='h-8 w-8 text-white' />
          </div>
          <h1 className='mb-1 text-3xl font-bold tracking-tight text-gray-900'>
            Cresce Feliz
          </h1>
          <p className='text-sm font-medium text-gray-600'>
            Painel Administrativo
          </p>
        </div>

        <Card shadow='lg' padding='lg' className='bg-white/80 backdrop-blur-xl'>
          <CardHeader>
            <CardTitle className='text-2xl'>Bem-vindo</CardTitle>
            <CardDescription>
              Aceda à sua conta de administrador
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-6'>
            <Form onSubmit={handleSubmit}>
              <FormField>
                <FormLabel htmlFor='email'>Email</FormLabel>
                <Input
                  id='email'
                  type='email'
                  name='email'
                  placeholder='<EMAIL>'
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className='bg-white/60 backdrop-blur-sm'
                />
              </FormField>

              <FormField>
                <FormLabel htmlFor='password'>Palavra-passe</FormLabel>
                <div className='relative'>
                  <Input
                    id='password'
                    type={showPassword ? 'text' : 'password'}
                    name='password'
                    placeholder='••••••••'
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    className='pr-11 bg-white/60 backdrop-blur-sm'
                  />
                  <button
                    type='button'
                    aria-label={
                      showPassword
                        ? 'Esconder palavra-passe'
                        : 'Mostrar palavra-passe'
                    }
                    onClick={() => setShowPassword(!showPassword)}
                    className='absolute inset-y-0 right-2 flex items-center text-gray-500 transition-colors hover:text-gray-700'
                  >
                    {showPassword ? (
                      <BsEyeSlash className='h-5 w-5' />
                    ) : (
                      <BsEye className='h-5 w-5' />
                    )}
                  </button>
                </div>
                <FormHelp>
                  Use credenciais fornecidas pelo administrador.
                </FormHelp>
              </FormField>

              <div className='flex items-center justify-between pt-1'>
                <label className='flex cursor-pointer items-center'>
                  <input
                    type='checkbox'
                    className='h-4 w-4 rounded border-gray-300 text-green-600 focus:ring-green-500 focus:ring-offset-0'
                  />
                  <span className='ml-2 text-xs font-medium text-gray-600'>
                    Lembrar-me
                  </span>
                </label>
                <a
                  href='#'
                  className='text-xs font-medium text-green-600 underline-offset-2 transition-colors hover:text-green-700 hover:underline'
                >
                  Esqueceu a palavra-passe?
                </a>
              </div>

              <FormActions className='mt-2'>
                <Button
                  type='submit'
                  className='w-full'
                  size='lg'
                  isLoading={isLoading}
                  loadingText='A processar...'
                >
                  Entrar
                </Button>
              </FormActions>
            </Form>
          </CardContent>
          <CardFooter className='flex flex-col'>
            <div className='mt-2 w-full border-t border-gray-200 pt-5'>
              <p className='mb-4 text-center text-xs text-gray-600'>
                Plataforma para gestão de infantários
              </p>
              <div className='grid grid-cols-3 gap-4'>
                <div className='text-center'>
                  <div className='mx-auto mb-2 flex h-10 w-10 items-center justify-center rounded-lg bg-green-100'>
                    <BsShield className='h-5 w-5 text-green-600' />
                  </div>
                  <p className='text-[11px] font-medium text-gray-600'>
                    Seguro
                  </p>
                </div>
                <div className='text-center'>
                  <div className='mx-auto mb-2 flex h-10 w-10 items-center justify-center rounded-lg bg-yellow-100'>
                    <BsPeople className='h-5 w-5 text-yellow-600' />
                  </div>
                  <p className='text-[11px] font-medium text-gray-600'>
                    Gestão
                  </p>
                </div>
                <div className='text-center'>
                  <div className='mx-auto mb-2 flex h-10 w-10 items-center justify-center rounded-lg bg-emerald-100'>
                    <BsBook className='h-5 w-5 text-emerald-600' />
                  </div>
                  <p className='text-[11px] font-medium text-gray-600'>
                    Educação
                  </p>
                </div>
              </div>
            </div>
          </CardFooter>
        </Card>

        <div className='mt-8 text-center'>
          <p className='text-xs text-gray-500'>
            © 2025 Cresce Feliz. Todos os direitos reservados.
          </p>
        </div>
      </div>
    </div>
  );
}
