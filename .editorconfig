# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

[*]
indent_style = space
indent_size = 2
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

[*.{js,jsx,ts,tsx}]
indent_size = 2
quote_type = single
curly_bracket_next_line = false
spaces_around_operators = true
spaces_around_brackets = false

[*.{json,json5}]
indent_size = 2

[*.{css,scss,sass}]
indent_size = 2

[*.md]
trim_trailing_whitespace = false
max_line_length = 100

[*.{yml,yaml}]
indent_size = 2

[*.html]
indent_size = 2

[Makefile]
indent_style = tab

[*.py]
indent_size = 4

[*.go]
indent_style = tab
indent_size = 4
