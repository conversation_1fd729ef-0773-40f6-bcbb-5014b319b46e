{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": true, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "embeddedLanguageFormatting": "auto", "tailwindFunctions": ["clsx", "cn", "cva"], "overrides": [{"files": "*.json", "options": {"printWidth": 120}}, {"files": "*.md", "options": {"printWidth": 100, "proseWrap": "always"}}]}